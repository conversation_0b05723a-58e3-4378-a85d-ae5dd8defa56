import { Button } from '@/components/ui/button';
import { <PERSON>, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { Activity, Calendar, Heart, Shield, Stethoscope, Users } from 'lucide-react';

export default function Welcome() {
    const { auth } = usePage<SharedData>().props;

    return (
        <>
            <Head title="Welcome to Dr. Club">
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
                {/* Navigation */}
                <header className="border-b bg-white/80 backdrop-blur-sm dark:bg-gray-900/80 dark:border-gray-800">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex h-16 items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600">
                                    <Stethoscope className="h-5 w-5 text-white" />
                                </div>
                                <span className="text-xl font-bold text-gray-900 dark:text-white">Dr. Club</span>
                            </div>
                            <nav className="flex items-center space-x-4">
                                {auth.user ? (
                                    <Button asChild>
                                        <Link href={route('dashboard')}>Dashboard</Link>
                                    </Button>
                                ) : (
                                    <>
                                        <Button variant="ghost" asChild>
                                            <Link href={route('login')}>Log in</Link>
                                        </Button>
                                        <Button asChild>
                                            <Link href={route('register')}>Get Started</Link>
                                        </Button>
                                    </>
                                )}
                            </nav>
                        </div>
                    </div>
                </header>

                {/* Hero Section */}
                <main className="mx-auto max-w-7xl px-4 py-16 sm:px-6 sm:py-24 lg:px-8">
                    <div className="text-center">
                        <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl dark:text-white">
                            Welcome to{' '}
                            <span className="bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                                Dr. Club
                            </span>
                        </h1>
                        <p className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-gray-600 dark:text-gray-300">
                            Your comprehensive healthcare management platform. Connect with healthcare professionals, manage appointments,
                            and take control of your health journey with our innovative digital solutions.
                        </p>
                        <div className="mt-10 flex items-center justify-center gap-x-6">
                            {auth.user ? (
                                <Button size="lg" asChild>
                                    <Link href={route('dashboard')}>Go to Dashboard</Link>
                                </Button>
                            ) : (
                                <>
                                    <Button size="lg" asChild>
                                        <Link href={route('register')}>Get Started</Link>
                                    </Button>
                                    <Button variant="outline" size="lg" asChild>
                                        <Link href={route('login')}>Sign In</Link>
                                    </Button>
                                </>
                            )}
                        </div>
                    </div>

                    {/* Features Section */}
                    <div className="mt-24">
                        <div className="text-center">
                            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl dark:text-white">
                                Everything you need for better healthcare
                            </h2>
                            <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
                                Comprehensive tools and features designed to enhance your healthcare experience
                            </p>
                        </div>

                        <div className="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
                            <Card className="border-0 bg-white/60 backdrop-blur-sm dark:bg-gray-800/60">
                                <CardHeader>
                                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900">
                                        <Calendar className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                                    </div>
                                    <CardTitle>Appointment Management</CardTitle>
                                    <CardDescription>
                                        Schedule, reschedule, and manage your medical appointments with ease
                                    </CardDescription>
                                </CardHeader>
                            </Card>

                            <Card className="border-0 bg-white/60 backdrop-blur-sm dark:bg-gray-800/60">
                                <CardHeader>
                                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900">
                                        <Heart className="h-6 w-6 text-green-600 dark:text-green-400" />
                                    </div>
                                    <CardTitle>Health Monitoring</CardTitle>
                                    <CardDescription>
                                        Track your vital signs, medications, and health metrics in one place
                                    </CardDescription>
                                </CardHeader>
                            </Card>

                            <Card className="border-0 bg-white/60 backdrop-blur-sm dark:bg-gray-800/60">
                                <CardHeader>
                                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900">
                                        <Users className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                                    </div>
                                    <CardTitle>Doctor Network</CardTitle>
                                    <CardDescription>
                                        Connect with qualified healthcare professionals in your area
                                    </CardDescription>
                                </CardHeader>
                            </Card>

                            <Card className="border-0 bg-white/60 backdrop-blur-sm dark:bg-gray-800/60">
                                <CardHeader>
                                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100 dark:bg-orange-900">
                                        <Shield className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                                    </div>
                                    <CardTitle>Secure Records</CardTitle>
                                    <CardDescription>
                                        Your medical records are encrypted and securely stored with HIPAA compliance
                                    </CardDescription>
                                </CardHeader>
                            </Card>

                            <Card className="border-0 bg-white/60 backdrop-blur-sm dark:bg-gray-800/60">
                                <CardHeader>
                                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-red-100 dark:bg-red-900">
                                        <Activity className="h-6 w-6 text-red-600 dark:text-red-400" />
                                    </div>
                                    <CardTitle>Health Analytics</CardTitle>
                                    <CardDescription>
                                        Get insights into your health trends with advanced analytics and reporting
                                    </CardDescription>
                                </CardHeader>
                            </Card>

                            <Card className="border-0 bg-white/60 backdrop-blur-sm dark:bg-gray-800/60">
                                <CardHeader>
                                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-teal-100 dark:bg-teal-900">
                                        <Stethoscope className="h-6 w-6 text-teal-600 dark:text-teal-400" />
                                    </div>
                                    <CardTitle>Telemedicine</CardTitle>
                                    <CardDescription>
                                        Virtual consultations and remote monitoring for convenient healthcare access
                                    </CardDescription>
                                </CardHeader>
                            </Card>
                        </div>
                    </div>

                    {/* Call to Action Section */}
                    <div className="mt-24 rounded-2xl bg-gradient-to-r from-blue-600 to-green-600 px-6 py-16 text-center">
                        <h2 className="text-3xl font-bold text-white sm:text-4xl">
                            Ready to transform your healthcare experience?
                        </h2>
                        <p className="mx-auto mt-4 max-w-2xl text-lg text-blue-100">
                            Join thousands of patients and healthcare providers who trust Dr. Club for their medical needs.
                        </p>
                        <div className="mt-8 flex flex-col items-center justify-center gap-4 sm:flex-row">
                            {!auth.user && (
                                <>
                                    <Button size="lg" variant="secondary" asChild>
                                        <Link href={route('register')}>Start Free Trial</Link>
                                    </Button>
                                    <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600" asChild>
                                        <Link href={route('login')}>Sign In</Link>
                                    </Button>
                                </>
                            )}
                        </div>
                    </div>
                </main>

                {/* Footer */}
                <footer className="border-t bg-white/80 backdrop-blur-sm dark:bg-gray-900/80 dark:border-gray-800">
                    <div className="mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <div className="flex items-center justify-center space-x-2">
                                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600">
                                    <Stethoscope className="h-5 w-5 text-white" />
                                </div>
                                <span className="text-xl font-bold text-gray-900 dark:text-white">Dr. Club</span>
                            </div>
                            <p className="mt-4 text-sm text-gray-600 dark:text-gray-400">
                                © 2024 Dr. Club. All rights reserved. Empowering healthcare through technology.
                            </p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}